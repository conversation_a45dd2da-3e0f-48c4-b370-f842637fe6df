.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 100vw;
  height: 305.6vw;
  overflow: hidden;
  .group_1 {
    width: 100vw;
    height: 23.2vw;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      margin-bottom: 0.27vw;
      .text_2 {
        width: 8.54vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 3.2vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 3.2vw 0 0;
      }
    }
  }
  .group_2 {
    background-color: rgba(11, 206, 148, 1);
    height: 53.34vw;
    margin-top: -0.26vw;
    width: 100vw;
    .group_3 {
      width: 99.47vw;
      height: 53.34vw;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG646d30f6a0c7e5fa5bec6d27bf576f5a.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin-left: 0.27vw;
      .group_4 {
        width: 93.87vw;
        height: 13.34vw;
        margin: 9.6vw 0 0 2.93vw;
        .image-text_1 {
          width: 37.87vw;
          height: 13.34vw;
          .label_1 {
            width: 12.8vw;
            height: 12.8vw;
          }
          .group_5 {
            width: 21.34vw;
            height: 12.27vw;
            margin-top: 1.07vw;
            .text-group_1 {
              width: 21.34vw;
              height: 5.87vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 5.33vw;
              font-family: Pinyon Script-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 5.87vw;
            }
            .group_6 {
              background-color: rgba(255, 255, 255, 0.3);
              border-radius: 100px;
              width: 14.67vw;
              height: 5.87vw;
              margin-top: 0.54vw;
              .block_1 {
                background-color: rgba(255, 255, 255, 1);
                width: 3.74vw;
                height: 3.2vw;
                margin: 1.6vw 0 0 2.93vw;
              }
              .text_3 {
                width: 2.14vw;
                height: 3.47vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 3.46vw;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 26.67vw;
                margin: 1.06vw 4vw 0 1.86vw;
              }
            }
          }
        }
        .image_2 {
          width: 5.6vw;
          height: 5.34vw;
          margin-top: 3.74vw;
        }
      }
      .thumbnail_4 {
        width: 2.4vw;
        height: 3.74vw;
        margin: 0.26vw 0 0 56.26vw;
      }
      .group_7 {
        background-image: linear-gradient(
          94deg,
          rgba(254, 217, 185, 1) 0,
          rgba(253, 240, 216, 1) 100%
        );
        border-radius: 10px 10px 0px 0px;
        height: 21.34vw;
        width: 93.34vw;
        margin: 3.46vw 0 1.6vw 3.2vw;
        .group_8 {
          width: 93.34vw;
          height: 21.34vw;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG68d4f493265117f583f1a42c06f9d7e9.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-text_2 {
            width: 40vw;
            height: 12vw;
            margin: 2.13vw 0 0 3.46vw;
            .label_2 {
              width: 12vw;
              height: 12vw;
            }
            .text-group_2 {
              width: 25.6vw;
              height: 4.27vw;
              overflow-wrap: break-word;
              color: rgba(104, 57, 45, 0.7);
              font-size: 4.26vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 4vw;
            }
          }
          .text-wrapper_1 {
            background-color: rgba(151, 96, 58, 1);
            border-radius: 38px;
            height: 7.2vw;
            width: 16.54vw;
            margin: 4.26vw 2.93vw 0 30.4vw;
            .text_4 {
              width: 13.07vw;
              height: 2.94vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 2.93vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 2.13vw 0 0 1.86vw;
            }
          }
        }
      }
    }
  }
  .group_9 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 53.87vw;
    width: 93.6vw;
    margin: 72.26vw 0 0 3.2vw;
    .box_3 {
      width: 84.54vw;
      height: 12.8vw;
      margin: 2.93vw 0 0 6.13vw;
      .image-text_3 {
        width: 6.94vw;
        height: 12.54vw;
        margin-top: 0.27vw;
        .label_3 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 1.07vw;
        }
        .text-group_3 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.47vw;
        }
      }
      .image-text_4 {
        width: 6.4vw;
        height: 12.54vw;
        margin: 0.26vw 0 0 11.73vw;
        .image_3 {
          width: 5.07vw;
          height: 6.14vw;
          margin-left: 1.07vw;
        }
        .text-group_4 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.2vw;
        }
      }
      .image-text_5 {
        width: 6.67vw;
        height: 12.54vw;
        margin: 0.26vw 0 0 12.26vw;
        .label_4 {
          width: 6.14vw;
          height: 5.6vw;
          margin-left: 0.54vw;
        }
        .text-group_5 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.74vw;
        }
      }
      .image-text_6 {
        width: 6.4vw;
        height: 12.8vw;
        margin-left: 12vw;
        .box_4 {
          height: 6.4vw;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG6fd2d1a43c0e26482a7cea3a26996afa.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 6.4vw;
          .image-wrapper_1 {
            background-color: rgba(75, 144, 248, 1);
            height: 6.14vw;
            width: 6.14vw;
            margin: 0.26vw 0 0 0.26vw;
            .label_5 {
              width: 6.14vw;
              height: 6.14vw;
            }
          }
        }
        .text-group_6 {
          width: 6.4vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.2vw;
        }
      }
      .image-text_7 {
        width: 12.8vw;
        height: 12.54vw;
        margin: 0.26vw 0 0 9.33vw;
        .label_6 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 4vw;
        }
        .text-group_7 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.47vw;
        }
      }
    }
    .box_5 {
      width: 87.74vw;
      height: 12vw;
      margin: 4.26vw 0 0 2.93vw;
      .image-text_8 {
        width: 12.8vw;
        height: 12vw;
        .image-wrapper_2 {
          height: 6.14vw;
          background: url(/static/lanhu_gerenzhongxin2/4d1adf7e23c3453c99230214a2ba611d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 4.27vw;
          width: 5.07vw;
          .image_4 {
            width: 5.07vw;
            height: 6.14vw;
          }
        }
        .text-group_8 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.67vw;
        }
      }
      .image-text_9 {
        width: 9.6vw;
        height: 12vw;
        margin-left: 7.47vw;
        .label_7 {
          width: 5.87vw;
          height: 5.87vw;
          margin-left: 1.07vw;
        }
        .text-group_9 {
          width: 9.6vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.94vw;
        }
      }
      .image-text_10 {
        width: 9.6vw;
        height: 11.74vw;
        margin: 0.26vw 0 0 9.06vw;
        .image-wrapper_3 {
          height: 5.34vw;
          background: url(/static/lanhu_gerenzhongxin2/1b7f2c88697d41d0818a0005068cd34d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 1.6vw;
          width: 6.14vw;
          .image_5 {
            width: 6.14vw;
            height: 5.34vw;
          }
        }
        .text-group_10 {
          width: 9.6vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 3.2vw;
        }
      }
      .image-text_11 {
        width: 12.8vw;
        height: 12vw;
        margin-left: 7.47vw;
        .image-wrapper_4 {
          height: 6.14vw;
          background: url(/static/lanhu_gerenzhongxin2/7a0577e2c38241f396213507d929bb95_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 3.2vw;
          width: 6.14vw;
          .label_8 {
            width: 6.14vw;
            height: 6.14vw;
          }
        }
        .text-group_11 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.67vw;
        }
      }
      .image-text_12 {
        width: 12.8vw;
        height: 12vw;
        margin-left: 6.14vw;
        .image-wrapper_5 {
          height: 5.87vw;
          background: url(/static/lanhu_gerenzhongxin2/4034f7295cd44c02a85a6c1b11a789f9_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 3.47vw;
          width: 5.07vw;
          .image_6 {
            width: 5.07vw;
            height: 5.87vw;
          }
        }
        .text-group_12 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.94vw;
        }
      }
    }
    .box_6 {
      width: 68.8vw;
      height: 12vw;
      margin: 4.53vw 0 5.33vw 2.93vw;
      .image-text_13 {
        width: 12.8vw;
        height: 12vw;
        .image-wrapper_6 {
          height: 5.87vw;
          background: url(/static/lanhu_gerenzhongxin2/1ca55f70706842d69457e704f456f449_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 3.2vw;
          width: 5.87vw;
          .label_9 {
            width: 5.87vw;
            height: 5.87vw;
          }
        }
        .text-group_13 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.94vw;
        }
      }
      .image-text_14 {
        width: 12.8vw;
        height: 12vw;
        margin-left: 5.87vw;
        .image-wrapper_7 {
          height: 6.14vw;
          background: url(/static/lanhu_gerenzhongxin2/a9797eee4ec44b468b37cefa99321402_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 3.2vw;
          width: 5.87vw;
          .label_10 {
            width: 5.87vw;
            height: 6.14vw;
          }
        }
        .text-group_14 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.67vw;
        }
      }
      .image-text_15 {
        width: 16vw;
        height: 12vw;
        margin-left: 4.27vw;
        .image-wrapper_8 {
          height: 6.14vw;
          background: url(/static/lanhu_gerenzhongxin2/b2d7338e8fb843e1a1ab90ef7c472ae1_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 5.07vw;
          width: 5.87vw;
          .label_11 {
            width: 5.87vw;
            height: 6.14vw;
          }
        }
        .text-group_15 {
          width: 16vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.67vw;
        }
      }
      .image-text_16 {
        width: 12.8vw;
        height: 12vw;
        margin-left: 4.27vw;
        .image_7 {
          width: 4.8vw;
          height: 5.87vw;
          margin-left: 4vw;
        }
        .text-group_16 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 2.94vw;
        }
      }
    }
  }
  .group_10 {
    width: 100vw;
    height: 83.2vw;
    .group_11 {
      width: 93.34vw;
      height: 5.34vw;
      margin: 4.26vw 0 0 3.2vw;
      .text_5 {
        width: 17.07vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.54vw;
      }
      .text_6 {
        width: 17.07vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 0.53vw 0 0 7.46vw;
      }
      .text_7 {
        width: 17.07vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 0.53vw 0 0 7.2vw;
      }
      .image-text_17 {
        width: 13.6vw;
        height: 5.34vw;
        margin-left: 13.87vw;
        .text-group_17 {
          width: 6.94vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin-top: 1.07vw;
        }
        .thumbnail_5 {
          width: 5.34vw;
          height: 5.34vw;
        }
      }
    }
    .group_12 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 50px;
      width: 13.6vw;
      height: 1.6vw;
      margin: 0.8vw 0 0 4.53vw;
    }
    .list_1 {
      width: 93.6vw;
      height: 64.54vw;
      justify-content: space-between;
      margin: 2.66vw 0 4vw 3.2vw;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 93.6vw;
        height: 30.94vw;
        margin-bottom: 2.67vw;
        .image-text_18 {
          position: relative;
          width: 64.27vw;
          height: 24.27vw;
          margin: 3.2vw 0 0 3.2vw;
          .image_8 {
            width: 24.27vw;
            height: 24.27vw;
          }
          .text-group_18 {
            width: 36.8vw;
            height: 23.2vw;
            margin: 0.53vw 0 0 2.93vw;
            .text_8 {
              width: 24vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 4vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text_9 {
              width: 33.07vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(152, 152, 152, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
            }
            .box_7 {
              width: 36.8vw;
              height: 4.8vw;
              margin-top: 8.54vw;
              .text-wrapper_2 {
                width: 10.94vw;
                height: 4.8vw;
                overflow-wrap: break-word;
                font-size: 0;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                line-height: 26.67vw;
                .text_10 {
                  width: 10.94vw;
                  height: 4.8vw;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 2.13vw;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  line-height: 26.67vw;
                }
                .text_11 {
                  width: 10.94vw;
                  height: 4.8vw;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 4.8vw;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 26.67vw;
                }
              }
              .text_12 {
                width: 13.87vw;
                height: 3.2vw;
                overflow-wrap: break-word;
                color: rgba(153, 153, 153, 1);
                font-size: 3.2vw;
                font-family: PingFang SC-Regular;
                text-decoration: line-through;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
                margin-top: 0.8vw;
              }
            }
          }
          .text-wrapper_3 {
            background-image: linear-gradient(
              90deg,
              rgba(255, 239, 190, 1) 0,
              rgba(254, 230, 157, 1) 100%
            );
            border-radius: 10px 10px 0px 10px;
            height: 4vw;
            width: 9.6vw;
            margin: 19.46vw 0 0 -24.8vw;
            .text_13 {
              width: 6.4vw;
              height: 2.14vw;
              overflow-wrap: break-word;
              color: rgba(98, 59, 4, 1);
              font-size: 2.13vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 1.06vw 0 0 1.6vw;
            }
          }
          .block_2 {
            border-radius: 4px;
            width: 20.54vw;
            height: 4vw;
            border: 1px solid rgba(231, 96, 81, 1);
            margin: 12.26vw 0 0 -5.06vw;
            .group_13 {
              background-color: rgba(231, 96, 81, 1);
              border-radius: 4px 0px 4px 0px;
              height: 4vw;
              width: 4.54vw;
              .group_14 {
                background-color: rgba(255, 207, 202, 1);
                width: 2.14vw;
                height: 2.67vw;
                margin: 0.53vw 0 0 1.33vw;
              }
            }
            .text_14 {
              width: 14.14vw;
              height: 2.14vw;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 2.66vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 0.8vw 0.8vw 0 1.06vw;
            }
          }
          .block_3 {
            background-image: linear-gradient(
              180deg,
              rgba(59, 41, 0, 1) 0,
              rgba(86, 54, 0, 1) 100%
            );
            border-radius: 4px;
            position: absolute;
            left: 27.2vw;
            top: 12.27vw;
            width: 14.94vw;
            height: 4vw;
            .image-text_19 {
              width: 12vw;
              height: 2.67vw;
              margin: 0.8vw 0 0 1.6vw;
              .block_4 {
                background-color: rgba(255, 234, 184, 1);
                width: 2.67vw;
                height: 2.67vw;
              }
              .text-group_19 {
                width: 8.54vw;
                height: 2.67vw;
                overflow-wrap: break-word;
                color: rgba(255, 234, 184, 1);
                font-size: 2.66vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
              }
            }
          }
        }
        .text-wrapper_4 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 60px;
          height: 6.67vw;
          width: 17.6vw;
          margin: 21.33vw 3.2vw 0 0;
          .text_15 {
            width: 12.8vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 1.86vw 0 0 2.4vw;
          }
        }
      }
    }
  }
  .group_15 {
    background-color: rgba(255, 255, 255, 1);
    width: 100vw;
    height: 13.07vw;
    margin: -0.26vw 0 7.2vw 0;
    .image-text_20 {
      width: 6.4vw;
      height: 10.4vw;
      margin-top: 1.6vw;
      .label_12 {
        width: 5.87vw;
        height: 5.87vw;
        margin-left: 0.27vw;
      }
      .text-group_20 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 1.34vw;
      }
    }
    .image-text_21 {
      width: 6.4vw;
      height: 10.14vw;
      margin-top: 1.87vw;
      .label_13 {
        width: 5.87vw;
        height: 5.87vw;
        margin-left: 0.27vw;
      }
      .text-group_21 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 1.07vw;
      }
    }
    .image-text_22 {
      width: 6.4vw;
      height: 10.14vw;
      margin-top: 1.87vw;
      .label_14 {
        width: 5.87vw;
        height: 5.87vw;
        margin-left: 0.27vw;
      }
      .text-group_22 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 1.07vw;
      }
    }
    .image-text_23 {
      width: 6.4vw;
      height: 10.14vw;
      margin-top: 1.87vw;
      .label_15 {
        width: 5.87vw;
        height: 5.87vw;
        margin-left: 0.27vw;
      }
      .text-group_23 {
        width: 6.4vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 1.07vw;
      }
    }
  }
  .group_16 {
    position: absolute;
    left: 0;
    top: 76.27vw;
    width: 100vw;
    height: 72.54vw;
    .box_8 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 93.6vw;
      height: 28.27vw;
      margin: 21.86vw 0 0 3.2vw;
      .block_5 {
        width: 86.94vw;
        height: 3.74vw;
        margin: 4.26vw 0 0 3.46vw;
        .text_16 {
          width: 14.94vw;
          height: 3.74vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 3.73vw;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_17 {
          width: 12.8vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 3.2vw;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 0.26vw 0 0 55.2vw;
        }
        .thumbnail_6 {
          width: 1.34vw;
          height: 2.14vw;
          margin: 0.8vw 0 0 2.66vw;
        }
      }
      .block_6 {
        width: 86.94vw;
        height: 12vw;
        margin: 4.53vw 0 3.73vw 4.53vw;
        .image-text_24 {
          width: 9.6vw;
          height: 12vw;
          .box_9 {
            height: 6.4vw;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG3a152a939d0da18d03352ba14032f56e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 1.6vw;
            width: 6.4vw;
            .text-wrapper_5 {
              background-color: rgba(238, 12, 12, 1);
              border-radius: 50%;
              height: 3.2vw;
              width: 3.2vw;
              margin: -0.53vw 0 0 4.8vw;
              .text_18 {
                width: 1.34vw;
                height: 2.67vw;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 2.66vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 26.67vw;
                margin: 0.26vw 0 0 0.8vw;
              }
            }
          }
          .text-group_24 {
            width: 9.6vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 3.2vw;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.4vw;
          }
        }
        .image-text_25 {
          width: 9.6vw;
          height: 12vw;
          margin-left: 9.07vw;
          .label_16 {
            width: 6.4vw;
            height: 6.67vw;
            margin-left: 1.6vw;
          }
          .text-group_25 {
            width: 9.6vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 3.2vw;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.14vw;
          }
        }
        .image-text_26 {
          width: 9.6vw;
          height: 12vw;
          margin-left: 9.07vw;
          .label_17 {
            width: 6.4vw;
            height: 6.4vw;
            margin-left: 1.6vw;
          }
          .text-group_26 {
            width: 9.6vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 3.2vw;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.4vw;
          }
        }
        .image-text_27 {
          width: 9.6vw;
          height: 12vw;
          margin-left: 9.07vw;
          .label_18 {
            width: 6.4vw;
            height: 6.4vw;
            margin-left: 1.6vw;
          }
          .text-group_27 {
            width: 9.6vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 3.2vw;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.4vw;
          }
        }
        .image-text_28 {
          width: 13.87vw;
          height: 12vw;
          margin-left: 7.47vw;
          .label_19 {
            width: 6.4vw;
            height: 6.67vw;
            margin-left: 3.2vw;
          }
          .text-group_28 {
            width: 13.87vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 3.2vw;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.14vw;
          }
        }
      }
    }
    .box_10 {
      width: 93.34vw;
      height: 16.8vw;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGfa2ef94064ba2c6d00469d8c83584628.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 2.66vw 0 2.93vw 3.2vw;
      .image-text_29 {
        width: 48.8vw;
        height: 10.4vw;
        margin: 3.46vw 0 0 4.53vw;
        .group_17 {
          height: 10.4vw;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG75681517c0627fcdab92e934834e4096.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 11.74vw;
          .group_18 {
            height: 8.8vw;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGe61461c9c7dc103306127b0493ef6139.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 10.4vw;
            margin: 1.6vw 0 0 -0.26vw;
            .box_11 {
              width: 3.74vw;
              height: 3.74vw;
              background: url(/static/lanhu_gerenzhongxin2/6ae66c597d174fe6aa4c9f81b4de731f_mergeImage.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 1.6vw 0 0 3.46vw;
            }
          }
        }
        .text-group_29 {
          width: 33.87vw;
          height: 9.6vw;
          .text_19 {
            width: 33.6vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4.8vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
          }
          .text_20 {
            width: 33.87vw;
            height: 2.67vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.66vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 2.14vw;
          }
        }
      }
      .text-wrapper_6 {
        background-color: rgba(0, 143, 116, 1);
        border-radius: 38px;
        height: 7.2vw;
        width: 16.54vw;
        margin: 4.8vw 3.2vw 0 20.26vw;
        .text_21 {
          width: 13.07vw;
          height: 2.94vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 2.93vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 2.13vw 0 0 1.86vw;
        }
      }
    }
    .box_12 {
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      position: absolute;
      left: 3.2vw;
      top: -7.46vw;
      width: 93.6vw;
      height: 26.67vw;
      .box_13 {
        background-color: rgba(254, 247, 239, 1);
        border-radius: 10px;
        width: 40.8vw;
        height: 18.94vw;
        margin: 3.73vw 0 0 3.46vw;
        .image-text_30 {
          position: relative;
          width: 38.14vw;
          height: 18.94vw;
          margin-left: 2.67vw;
          .text-group_30 {
            width: 23.47vw;
            height: 12.27vw;
            margin-top: 2.67vw;
            .text_22 {
              width: 12.8vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.2vw;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-left: 4.8vw;
            }
            .text_23 {
              width: 23.47vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 6.4vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
            }
          }
          .text-wrapper_7 {
            height: 18.94vw;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG876225c7c7c7e608ee2a030b4a8f33b3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 13.87vw;
            .text_24 {
              width: 6.4vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 8vw 0 0 5.33vw;
            }
          }
          .section_1 {
            background-color: rgba(231, 96, 81, 1);
            position: absolute;
            left: 0;
            top: 2.67vw;
            width: 2.94vw;
            height: 3.2vw;
          }
        }
      }
      .box_14 {
        background-color: rgba(237, 249, 243, 1);
        border-radius: 10px;
        width: 40.8vw;
        height: 18.94vw;
        margin: 3.73vw 4.8vw 0 3.73vw;
        .image-text_31 {
          width: 38.14vw;
          height: 18.94vw;
          margin-left: 2.67vw;
          .text-group_31 {
            width: 20.8vw;
            height: 12.27vw;
            margin-top: 2.67vw;
            .text_25 {
              width: 16vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.2vw;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-left: 4.8vw;
            }
            .text-wrapper_8 {
              width: 8vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
              .text_26 {
                width: 8vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 6.4vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 26.67vw;
              }
              .text_27 {
                width: 8vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 4vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 26.67vw;
              }
            }
          }
          .text-wrapper_9 {
            height: 18.94vw;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGeafee3672b6f7f4ed5a9cff4ab408f2b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 13.87vw;
            .text_28 {
              width: 6.4vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 26.67vw;
              margin: 7.73vw 0 0 4.8vw;
            }
          }
        }
        .thumbnail_7 {
          width: 3.74vw;
          height: 3.74vw;
          margin: 2.4vw 34.13vw 0 -37.86vw;
        }
      }
    }
  }
}
