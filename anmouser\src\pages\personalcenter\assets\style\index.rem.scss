.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 10rem;
  height: 30.56rem;
  overflow: hidden;
  .group_1 {
    width: 10rem;
    height: 2.32rem;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      margin-bottom: 0.027rem;
      .text_2 {
        width: 0.854rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.32rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.32rem 0 0;
      }
    }
  }
  .group_2 {
    background-color: rgba(11, 206, 148, 1);
    height: 5.334rem;
    margin-top: -0.026rem;
    width: 10rem;
    .group_3 {
      width: 9.947rem;
      height: 5.334rem;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG646d30f6a0c7e5fa5bec6d27bf576f5a.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin-left: 0.027rem;
      .group_4 {
        width: 9.387rem;
        height: 1.334rem;
        margin: 0.96rem 0 0 0.294rem;
        .image-text_1 {
          width: 3.787rem;
          height: 1.334rem;
          .label_1 {
            width: 1.28rem;
            height: 1.28rem;
          }
          .group_5 {
            width: 2.134rem;
            height: 1.227rem;
            margin-top: 0.107rem;
            .text-group_1 {
              width: 2.134rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.533rem;
              font-family: Pinyon Script-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 0.587rem;
            }
            .group_6 {
              background-color: rgba(255, 255, 255, 0.3);
              border-radius: 100px;
              width: 1.467rem;
              height: 0.587rem;
              margin-top: 0.054rem;
              .block_1 {
                background-color: rgba(255, 255, 255, 1);
                width: 0.374rem;
                height: 0.32rem;
                margin: 0.16rem 0 0 0.294rem;
              }
              .text_3 {
                width: 0.214rem;
                height: 0.347rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.346rem;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 2.667rem;
                margin: 0.107rem 0.4rem 0 0.187rem;
              }
            }
          }
        }
        .image_2 {
          width: 0.56rem;
          height: 0.534rem;
          margin-top: 0.374rem;
        }
      }
      .thumbnail_4 {
        width: 0.24rem;
        height: 0.374rem;
        margin: 0.027rem 0 0 5.627rem;
      }
      .group_7 {
        background-image: linear-gradient(
          94deg,
          rgba(254, 217, 185, 1) 0,
          rgba(253, 240, 216, 1) 100%
        );
        border-radius: 10px 10px 0px 0px;
        height: 2.134rem;
        width: 9.334rem;
        margin: 0.347rem 0 0.16rem 0.32rem;
        .group_8 {
          width: 9.334rem;
          height: 2.134rem;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG68d4f493265117f583f1a42c06f9d7e9.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-text_2 {
            width: 4rem;
            height: 1.2rem;
            margin: 0.214rem 0 0 0.347rem;
            .label_2 {
              width: 1.2rem;
              height: 1.2rem;
            }
            .text-group_2 {
              width: 2.56rem;
              height: 0.427rem;
              overflow-wrap: break-word;
              color: rgba(104, 57, 45, 0.7);
              font-size: 0.426rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.4rem;
            }
          }
          .text-wrapper_1 {
            background-color: rgba(151, 96, 58, 1);
            border-radius: 38px;
            height: 0.72rem;
            width: 1.654rem;
            margin: 0.427rem 0.294rem 0 3.04rem;
            .text_4 {
              width: 1.307rem;
              height: 0.294rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.293rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.214rem 0 0 0.187rem;
            }
          }
        }
      }
    }
  }
  .group_9 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 5.387rem;
    width: 9.36rem;
    margin: 7.227rem 0 0 0.32rem;
    .box_3 {
      width: 8.454rem;
      height: 1.28rem;
      margin: 0.294rem 0 0 0.614rem;
      .image-text_3 {
        width: 0.694rem;
        height: 1.254rem;
        margin-top: 0.027rem;
        .label_3 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.107rem;
        }
        .text-group_3 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.347rem;
        }
      }
      .image-text_4 {
        width: 0.64rem;
        height: 1.254rem;
        margin: 0.027rem 0 0 1.174rem;
        .image_3 {
          width: 0.507rem;
          height: 0.614rem;
          margin-left: 0.107rem;
        }
        .text-group_4 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.32rem;
        }
      }
      .image-text_5 {
        width: 0.667rem;
        height: 1.254rem;
        margin: 0.027rem 0 0 1.227rem;
        .label_4 {
          width: 0.614rem;
          height: 0.56rem;
          margin-left: 0.054rem;
        }
        .text-group_5 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.374rem;
        }
      }
      .image-text_6 {
        width: 0.64rem;
        height: 1.28rem;
        margin-left: 1.2rem;
        .box_4 {
          height: 0.64rem;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG6fd2d1a43c0e26482a7cea3a26996afa.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 0.64rem;
          .image-wrapper_1 {
            background-color: rgba(75, 144, 248, 1);
            height: 0.614rem;
            width: 0.614rem;
            margin: 0.027rem 0 0 0.027rem;
            .label_5 {
              width: 0.614rem;
              height: 0.614rem;
            }
          }
        }
        .text-group_6 {
          width: 0.64rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.32rem;
        }
      }
      .image-text_7 {
        width: 1.28rem;
        height: 1.254rem;
        margin: 0.027rem 0 0 0.934rem;
        .label_6 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.4rem;
        }
        .text-group_7 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.347rem;
        }
      }
    }
    .box_5 {
      width: 8.774rem;
      height: 1.2rem;
      margin: 0.427rem 0 0 0.294rem;
      .image-text_8 {
        width: 1.28rem;
        height: 1.2rem;
        .image-wrapper_2 {
          height: 0.614rem;
          background: url(/static/lanhu_gerenzhongxin2/4d1adf7e23c3453c99230214a2ba611d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.427rem;
          width: 0.507rem;
          .image_4 {
            width: 0.507rem;
            height: 0.614rem;
          }
        }
        .text-group_8 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.267rem;
        }
      }
      .image-text_9 {
        width: 0.96rem;
        height: 1.2rem;
        margin-left: 0.747rem;
        .label_7 {
          width: 0.587rem;
          height: 0.587rem;
          margin-left: 0.107rem;
        }
        .text-group_9 {
          width: 0.96rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.294rem;
        }
      }
      .image-text_10 {
        width: 0.96rem;
        height: 1.174rem;
        margin: 0.027rem 0 0 0.907rem;
        .image-wrapper_3 {
          height: 0.534rem;
          background: url(/static/lanhu_gerenzhongxin2/1b7f2c88697d41d0818a0005068cd34d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.16rem;
          width: 0.614rem;
          .image_5 {
            width: 0.614rem;
            height: 0.534rem;
          }
        }
        .text-group_10 {
          width: 0.96rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.32rem;
        }
      }
      .image-text_11 {
        width: 1.28rem;
        height: 1.2rem;
        margin-left: 0.747rem;
        .image-wrapper_4 {
          height: 0.614rem;
          background: url(/static/lanhu_gerenzhongxin2/7a0577e2c38241f396213507d929bb95_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.32rem;
          width: 0.614rem;
          .label_8 {
            width: 0.614rem;
            height: 0.614rem;
          }
        }
        .text-group_11 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.267rem;
        }
      }
      .image-text_12 {
        width: 1.28rem;
        height: 1.2rem;
        margin-left: 0.614rem;
        .image-wrapper_5 {
          height: 0.587rem;
          background: url(/static/lanhu_gerenzhongxin2/4034f7295cd44c02a85a6c1b11a789f9_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.347rem;
          width: 0.507rem;
          .image_6 {
            width: 0.507rem;
            height: 0.587rem;
          }
        }
        .text-group_12 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.294rem;
        }
      }
    }
    .box_6 {
      width: 6.88rem;
      height: 1.2rem;
      margin: 0.454rem 0 0.534rem 0.294rem;
      .image-text_13 {
        width: 1.28rem;
        height: 1.2rem;
        .image-wrapper_6 {
          height: 0.587rem;
          background: url(/static/lanhu_gerenzhongxin2/1ca55f70706842d69457e704f456f449_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.32rem;
          width: 0.587rem;
          .label_9 {
            width: 0.587rem;
            height: 0.587rem;
          }
        }
        .text-group_13 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.294rem;
        }
      }
      .image-text_14 {
        width: 1.28rem;
        height: 1.2rem;
        margin-left: 0.587rem;
        .image-wrapper_7 {
          height: 0.614rem;
          background: url(/static/lanhu_gerenzhongxin2/a9797eee4ec44b468b37cefa99321402_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.32rem;
          width: 0.587rem;
          .label_10 {
            width: 0.587rem;
            height: 0.614rem;
          }
        }
        .text-group_14 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.267rem;
        }
      }
      .image-text_15 {
        width: 1.6rem;
        height: 1.2rem;
        margin-left: 0.427rem;
        .image-wrapper_8 {
          height: 0.614rem;
          background: url(/static/lanhu_gerenzhongxin2/b2d7338e8fb843e1a1ab90ef7c472ae1_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 0.507rem;
          width: 0.587rem;
          .label_11 {
            width: 0.587rem;
            height: 0.614rem;
          }
        }
        .text-group_15 {
          width: 1.6rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.267rem;
        }
      }
      .image-text_16 {
        width: 1.28rem;
        height: 1.2rem;
        margin-left: 0.427rem;
        .image_7 {
          width: 0.48rem;
          height: 0.587rem;
          margin-left: 0.4rem;
        }
        .text-group_16 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.294rem;
        }
      }
    }
  }
  .group_10 {
    width: 10rem;
    height: 8.32rem;
    .group_11 {
      width: 9.334rem;
      height: 0.534rem;
      margin: 0.427rem 0 0 0.32rem;
      .text_5 {
        width: 1.707rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.054rem;
      }
      .text_6 {
        width: 1.707rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.054rem 0 0 0.747rem;
      }
      .text_7 {
        width: 1.707rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.054rem 0 0 0.72rem;
      }
      .image-text_17 {
        width: 1.36rem;
        height: 0.534rem;
        margin-left: 1.387rem;
        .text-group_17 {
          width: 0.694rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin-top: 0.107rem;
        }
        .thumbnail_5 {
          width: 0.534rem;
          height: 0.534rem;
        }
      }
    }
    .group_12 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 50px;
      width: 1.36rem;
      height: 0.16rem;
      margin: 0.08rem 0 0 0.454rem;
    }
    .list_1 {
      width: 9.36rem;
      height: 6.454rem;
      justify-content: space-between;
      margin: 0.267rem 0 0.4rem 0.32rem;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 9.36rem;
        height: 3.094rem;
        margin-bottom: 0.267rem;
        .image-text_18 {
          position: relative;
          width: 6.427rem;
          height: 2.427rem;
          margin: 0.32rem 0 0 0.32rem;
          .image_8 {
            width: 2.427rem;
            height: 2.427rem;
          }
          .text-group_18 {
            width: 3.68rem;
            height: 2.32rem;
            margin: 0.054rem 0 0 0.294rem;
            .text_8 {
              width: 2.4rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.4rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text_9 {
              width: 3.307rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(152, 152, 152, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
            }
            .box_7 {
              width: 3.68rem;
              height: 0.48rem;
              margin-top: 0.854rem;
              .text-wrapper_2 {
                width: 1.094rem;
                height: 0.48rem;
                overflow-wrap: break-word;
                font-size: 0;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                line-height: 2.667rem;
                .text_10 {
                  width: 1.094rem;
                  height: 0.48rem;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 0.213rem;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  line-height: 2.667rem;
                }
                .text_11 {
                  width: 1.094rem;
                  height: 0.48rem;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 0.48rem;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 2.667rem;
                }
              }
              .text_12 {
                width: 1.387rem;
                height: 0.32rem;
                overflow-wrap: break-word;
                color: rgba(153, 153, 153, 1);
                font-size: 0.32rem;
                font-family: PingFang SC-Regular;
                text-decoration: line-through;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
                margin-top: 0.08rem;
              }
            }
          }
          .text-wrapper_3 {
            background-image: linear-gradient(
              90deg,
              rgba(255, 239, 190, 1) 0,
              rgba(254, 230, 157, 1) 100%
            );
            border-radius: 10px 10px 0px 10px;
            height: 0.4rem;
            width: 0.96rem;
            margin: 1.947rem 0 0 -2.48rem;
            .text_13 {
              width: 0.64rem;
              height: 0.214rem;
              overflow-wrap: break-word;
              color: rgba(98, 59, 4, 1);
              font-size: 0.213rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.107rem 0 0 0.16rem;
            }
          }
          .block_2 {
            border-radius: 4px;
            width: 2.054rem;
            height: 0.4rem;
            border: 1px solid rgba(231, 96, 81, 1);
            margin: 1.227rem 0 0 -0.507rem;
            .group_13 {
              background-color: rgba(231, 96, 81, 1);
              border-radius: 4px 0px 4px 0px;
              height: 0.4rem;
              width: 0.454rem;
              .group_14 {
                background-color: rgba(255, 207, 202, 1);
                width: 0.214rem;
                height: 0.267rem;
                margin: 0.054rem 0 0 0.134rem;
              }
            }
            .text_14 {
              width: 1.414rem;
              height: 0.214rem;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 0.266rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.08rem 0.08rem 0 0.107rem;
            }
          }
          .block_3 {
            background-image: linear-gradient(
              180deg,
              rgba(59, 41, 0, 1) 0,
              rgba(86, 54, 0, 1) 100%
            );
            border-radius: 4px;
            position: absolute;
            left: 2.72rem;
            top: 1.227rem;
            width: 1.494rem;
            height: 0.4rem;
            .image-text_19 {
              width: 1.2rem;
              height: 0.267rem;
              margin: 0.08rem 0 0 0.16rem;
              .block_4 {
                background-color: rgba(255, 234, 184, 1);
                width: 0.267rem;
                height: 0.267rem;
              }
              .text-group_19 {
                width: 0.854rem;
                height: 0.267rem;
                overflow-wrap: break-word;
                color: rgba(255, 234, 184, 1);
                font-size: 0.266rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
              }
            }
          }
        }
        .text-wrapper_4 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 60px;
          height: 0.667rem;
          width: 1.76rem;
          margin: 2.134rem 0.32rem 0 0;
          .text_15 {
            width: 1.28rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.187rem 0 0 0.24rem;
          }
        }
      }
    }
  }
  .group_15 {
    background-color: rgba(255, 255, 255, 1);
    width: 10rem;
    height: 1.307rem;
    margin: -0.027rem 0 0.72rem 0;
    .image-text_20 {
      width: 0.64rem;
      height: 1.04rem;
      margin-top: 0.16rem;
      .label_12 {
        width: 0.587rem;
        height: 0.587rem;
        margin-left: 0.027rem;
      }
      .text-group_20 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.134rem;
      }
    }
    .image-text_21 {
      width: 0.64rem;
      height: 1.014rem;
      margin-top: 0.187rem;
      .label_13 {
        width: 0.587rem;
        height: 0.587rem;
        margin-left: 0.027rem;
      }
      .text-group_21 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.107rem;
      }
    }
    .image-text_22 {
      width: 0.64rem;
      height: 1.014rem;
      margin-top: 0.187rem;
      .label_14 {
        width: 0.587rem;
        height: 0.587rem;
        margin-left: 0.027rem;
      }
      .text-group_22 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.107rem;
      }
    }
    .image-text_23 {
      width: 0.64rem;
      height: 1.014rem;
      margin-top: 0.187rem;
      .label_15 {
        width: 0.587rem;
        height: 0.587rem;
        margin-left: 0.027rem;
      }
      .text-group_23 {
        width: 0.64rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.107rem;
      }
    }
  }
  .group_16 {
    position: absolute;
    left: 0;
    top: 7.627rem;
    width: 10rem;
    height: 7.254rem;
    .box_8 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 9.36rem;
      height: 2.827rem;
      margin: 2.187rem 0 0 0.32rem;
      .block_5 {
        width: 8.694rem;
        height: 0.374rem;
        margin: 0.427rem 0 0 0.347rem;
        .text_16 {
          width: 1.494rem;
          height: 0.374rem;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.373rem;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_17 {
          width: 1.28rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.32rem;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.027rem 0 0 5.52rem;
        }
        .thumbnail_6 {
          width: 0.134rem;
          height: 0.214rem;
          margin: 0.08rem 0 0 0.267rem;
        }
      }
      .block_6 {
        width: 8.694rem;
        height: 1.2rem;
        margin: 0.454rem 0 0.374rem 0.454rem;
        .image-text_24 {
          width: 0.96rem;
          height: 1.2rem;
          .box_9 {
            height: 0.64rem;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG3a152a939d0da18d03352ba14032f56e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 0.16rem;
            width: 0.64rem;
            .text-wrapper_5 {
              background-color: rgba(238, 12, 12, 1);
              border-radius: 50%;
              height: 0.32rem;
              width: 0.32rem;
              margin: -0.054rem 0 0 0.48rem;
              .text_18 {
                width: 0.134rem;
                height: 0.267rem;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 0.266rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 2.667rem;
                margin: 0.027rem 0 0 0.08rem;
              }
            }
          }
          .text-group_24 {
            width: 0.96rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.32rem;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.24rem;
          }
        }
        .image-text_25 {
          width: 0.96rem;
          height: 1.2rem;
          margin-left: 0.907rem;
          .label_16 {
            width: 0.64rem;
            height: 0.667rem;
            margin-left: 0.16rem;
          }
          .text-group_25 {
            width: 0.96rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.32rem;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.214rem;
          }
        }
        .image-text_26 {
          width: 0.96rem;
          height: 1.2rem;
          margin-left: 0.907rem;
          .label_17 {
            width: 0.64rem;
            height: 0.64rem;
            margin-left: 0.16rem;
          }
          .text-group_26 {
            width: 0.96rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.32rem;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.24rem;
          }
        }
        .image-text_27 {
          width: 0.96rem;
          height: 1.2rem;
          margin-left: 0.907rem;
          .label_18 {
            width: 0.64rem;
            height: 0.64rem;
            margin-left: 0.16rem;
          }
          .text-group_27 {
            width: 0.96rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.32rem;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.24rem;
          }
        }
        .image-text_28 {
          width: 1.387rem;
          height: 1.2rem;
          margin-left: 0.747rem;
          .label_19 {
            width: 0.64rem;
            height: 0.667rem;
            margin-left: 0.32rem;
          }
          .text-group_28 {
            width: 1.387rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.32rem;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.214rem;
          }
        }
      }
    }
    .box_10 {
      width: 9.334rem;
      height: 1.68rem;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGfa2ef94064ba2c6d00469d8c83584628.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 0.267rem 0 0.294rem 0.32rem;
      .image-text_29 {
        width: 4.88rem;
        height: 1.04rem;
        margin: 0.347rem 0 0 0.454rem;
        .group_17 {
          height: 1.04rem;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG75681517c0627fcdab92e934834e4096.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 1.174rem;
          .group_18 {
            height: 0.88rem;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGe61461c9c7dc103306127b0493ef6139.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 1.04rem;
            margin: 0.16rem 0 0 -0.027rem;
            .box_11 {
              width: 0.374rem;
              height: 0.374rem;
              background: url(/static/lanhu_gerenzhongxin2/6ae66c597d174fe6aa4c9f81b4de731f_mergeImage.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 0.16rem 0 0 0.347rem;
            }
          }
        }
        .text-group_29 {
          width: 3.387rem;
          height: 0.96rem;
          .text_19 {
            width: 3.36rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.48rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
          }
          .text_20 {
            width: 3.387rem;
            height: 0.267rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.266rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.214rem;
          }
        }
      }
      .text-wrapper_6 {
        background-color: rgba(0, 143, 116, 1);
        border-radius: 38px;
        height: 0.72rem;
        width: 1.654rem;
        margin: 0.48rem 0.32rem 0 2.027rem;
        .text_21 {
          width: 1.307rem;
          height: 0.294rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.293rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.214rem 0 0 0.187rem;
        }
      }
    }
    .box_12 {
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      position: absolute;
      left: 0.32rem;
      top: -0.746rem;
      width: 9.36rem;
      height: 2.667rem;
      .box_13 {
        background-color: rgba(254, 247, 239, 1);
        border-radius: 10px;
        width: 4.08rem;
        height: 1.894rem;
        margin: 0.374rem 0 0 0.347rem;
        .image-text_30 {
          position: relative;
          width: 3.814rem;
          height: 1.894rem;
          margin-left: 0.267rem;
          .text-group_30 {
            width: 2.347rem;
            height: 1.227rem;
            margin-top: 0.267rem;
            .text_22 {
              width: 1.28rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.32rem;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-left: 0.48rem;
            }
            .text_23 {
              width: 2.347rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.64rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
            }
          }
          .text-wrapper_7 {
            height: 1.894rem;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG876225c7c7c7e608ee2a030b4a8f33b3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 1.387rem;
            .text_24 {
              width: 0.64rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.8rem 0 0 0.534rem;
            }
          }
          .section_1 {
            background-color: rgba(231, 96, 81, 1);
            position: absolute;
            left: 0;
            top: 0.267rem;
            width: 0.294rem;
            height: 0.32rem;
          }
        }
      }
      .box_14 {
        background-color: rgba(237, 249, 243, 1);
        border-radius: 10px;
        width: 4.08rem;
        height: 1.894rem;
        margin: 0.374rem 0.48rem 0 0.374rem;
        .image-text_31 {
          width: 3.814rem;
          height: 1.894rem;
          margin-left: 0.267rem;
          .text-group_31 {
            width: 2.08rem;
            height: 1.227rem;
            margin-top: 0.267rem;
            .text_25 {
              width: 1.6rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.32rem;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-left: 0.48rem;
            }
            .text-wrapper_8 {
              width: 0.8rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
              .text_26 {
                width: 0.8rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.64rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 2.667rem;
              }
              .text_27 {
                width: 0.8rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.4rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 2.667rem;
              }
            }
          }
          .text-wrapper_9 {
            height: 1.894rem;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGeafee3672b6f7f4ed5a9cff4ab408f2b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 1.387rem;
            .text_28 {
              width: 0.64rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 2.667rem;
              margin: 0.774rem 0 0 0.48rem;
            }
          }
        }
        .thumbnail_7 {
          width: 0.374rem;
          height: 0.374rem;
          margin: 0.24rem 3.414rem 0 -3.787rem;
        }
      }
    }
  }
}
