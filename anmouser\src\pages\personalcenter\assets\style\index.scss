.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 375px;
  height: 1146px;
  overflow: hidden;
  .group_1 {
    width: 375px;
    height: 87px;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 32px;
      .text_1 {
        width: 32px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 150px;
        margin: 7px 0 0 16px;
      }
      .thumbnail_1 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 251px;
      }
      .thumbnail_2 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 3px;
      }
      .thumbnail_3 {
        width: 19px;
        height: 19px;
        margin: 7px 15px 0 3px;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 54px;
      margin-bottom: 1px;
      .text_2 {
        width: 32px;
        height: 22px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 16px;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 22px;
        margin: 16px 0 0 12px;
      }
      .image_1 {
        width: 87px;
        height: 32px;
        margin: 11px 12px 0 0;
      }
    }
  }
  .group_2 {
    background-color: rgba(11, 206, 148, 1);
    height: 200px;
    margin-top: -1px;
    width: 375px;
    .group_3 {
      width: 373px;
      height: 200px;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG646d30f6a0c7e5fa5bec6d27bf576f5a.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin-left: 1px;
      .group_4 {
        width: 352px;
        height: 50px;
        margin: 36px 0 0 11px;
        .image-text_1 {
          width: 142px;
          height: 50px;
          .label_1 {
            width: 48px;
            height: 48px;
          }
          .group_5 {
            width: 80px;
            height: 46px;
            margin-top: 4px;
            .text-group_1 {
              width: 80px;
              height: 22px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 20px;
              font-family: Pinyon Script-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 22px;
            }
            .group_6 {
              background-color: rgba(255, 255, 255, 0.3);
              border-radius: 100px;
              width: 55px;
              height: 22px;
              margin-top: 2px;
              .block_1 {
                background-color: rgba(255, 255, 255, 1);
                width: 14px;
                height: 12px;
                margin: 6px 0 0 11px;
              }
              .text_3 {
                width: 8px;
                height: 13px;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 13px;
                font-family: Source Han Sans CN-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 100px;
                margin: 4px 15px 0 7px;
              }
            }
          }
        }
        .image_2 {
          width: 21px;
          height: 20px;
          margin-top: 14px;
        }
      }
      .thumbnail_4 {
        width: 9px;
        height: 14px;
        margin: 1px 0 0 211px;
      }
      .group_7 {
        background-image: linear-gradient(
          94deg,
          rgba(254, 217, 185, 1) 0,
          rgba(253, 240, 216, 1) 100%
        );
        border-radius: 10px 10px 0px 0px;
        height: 80px;
        width: 350px;
        margin: 13px 0 6px 12px;
        .group_8 {
          width: 350px;
          height: 80px;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG68d4f493265117f583f1a42c06f9d7e9.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-text_2 {
            width: 150px;
            height: 45px;
            margin: 8px 0 0 13px;
            .label_2 {
              width: 45px;
              height: 45px;
            }
            .text-group_2 {
              width: 96px;
              height: 16px;
              overflow-wrap: break-word;
              color: rgba(104, 57, 45, 0.7);
              font-size: 16px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin-top: 15px;
            }
          }
          .text-wrapper_1 {
            background-color: rgba(151, 96, 58, 1);
            border-radius: 38px;
            height: 27px;
            width: 62px;
            margin: 16px 11px 0 114px;
            .text_4 {
              width: 49px;
              height: 11px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 11px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 8px 0 0 7px;
            }
          }
        }
      }
    }
  }
  .group_9 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 202px;
    width: 351px;
    margin: 271px 0 0 12px;
    .box_3 {
      width: 317px;
      height: 48px;
      margin: 11px 0 0 23px;
      .image-text_3 {
        width: 26px;
        height: 47px;
        margin-top: 1px;
        .label_3 {
          width: 22px;
          height: 22px;
          margin-left: 4px;
        }
        .text-group_3 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 13px;
        }
      }
      .image-text_4 {
        width: 24px;
        height: 47px;
        margin: 1px 0 0 44px;
        .image_3 {
          width: 19px;
          height: 23px;
          margin-left: 4px;
        }
        .text-group_4 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 12px;
        }
      }
      .image-text_5 {
        width: 25px;
        height: 47px;
        margin: 1px 0 0 46px;
        .label_4 {
          width: 23px;
          height: 21px;
          margin-left: 2px;
        }
        .text-group_5 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 14px;
        }
      }
      .image-text_6 {
        width: 24px;
        height: 48px;
        margin-left: 45px;
        .box_4 {
          height: 24px;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG6fd2d1a43c0e26482a7cea3a26996afa.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 24px;
          .image-wrapper_1 {
            background-color: rgba(75, 144, 248, 1);
            height: 23px;
            width: 23px;
            margin: 1px 0 0 1px;
            .label_5 {
              width: 23px;
              height: 23px;
            }
          }
        }
        .text-group_6 {
          width: 24px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 12px;
        }
      }
      .image-text_7 {
        width: 48px;
        height: 47px;
        margin: 1px 0 0 35px;
        .label_6 {
          width: 22px;
          height: 22px;
          margin-left: 15px;
        }
        .text-group_7 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 13px;
        }
      }
    }
    .box_5 {
      width: 329px;
      height: 45px;
      margin: 16px 0 0 11px;
      .image-text_8 {
        width: 48px;
        height: 45px;
        .image-wrapper_2 {
          height: 23px;
          background: url(/static/lanhu_gerenzhongxin2/4d1adf7e23c3453c99230214a2ba611d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 16px;
          width: 19px;
          .image_4 {
            width: 19px;
            height: 23px;
          }
        }
        .text-group_8 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 10px;
        }
      }
      .image-text_9 {
        width: 36px;
        height: 45px;
        margin-left: 28px;
        .label_7 {
          width: 22px;
          height: 22px;
          margin-left: 4px;
        }
        .text-group_9 {
          width: 36px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 11px;
        }
      }
      .image-text_10 {
        width: 36px;
        height: 44px;
        margin: 1px 0 0 34px;
        .image-wrapper_3 {
          height: 20px;
          background: url(/static/lanhu_gerenzhongxin2/1b7f2c88697d41d0818a0005068cd34d_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 6px;
          width: 23px;
          .image_5 {
            width: 23px;
            height: 20px;
          }
        }
        .text-group_10 {
          width: 36px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 12px;
        }
      }
      .image-text_11 {
        width: 48px;
        height: 45px;
        margin-left: 28px;
        .image-wrapper_4 {
          height: 23px;
          background: url(/static/lanhu_gerenzhongxin2/7a0577e2c38241f396213507d929bb95_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 12px;
          width: 23px;
          .label_8 {
            width: 23px;
            height: 23px;
          }
        }
        .text-group_11 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 10px;
        }
      }
      .image-text_12 {
        width: 48px;
        height: 45px;
        margin-left: 23px;
        .image-wrapper_5 {
          height: 22px;
          background: url(/static/lanhu_gerenzhongxin2/4034f7295cd44c02a85a6c1b11a789f9_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 13px;
          width: 19px;
          .image_6 {
            width: 19px;
            height: 22px;
          }
        }
        .text-group_12 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 11px;
        }
      }
    }
    .box_6 {
      width: 258px;
      height: 45px;
      margin: 17px 0 20px 11px;
      .image-text_13 {
        width: 48px;
        height: 45px;
        .image-wrapper_6 {
          height: 22px;
          background: url(/static/lanhu_gerenzhongxin2/1ca55f70706842d69457e704f456f449_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 12px;
          width: 22px;
          .label_9 {
            width: 22px;
            height: 22px;
          }
        }
        .text-group_13 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 11px;
        }
      }
      .image-text_14 {
        width: 48px;
        height: 45px;
        margin-left: 22px;
        .image-wrapper_7 {
          height: 23px;
          background: url(/static/lanhu_gerenzhongxin2/a9797eee4ec44b468b37cefa99321402_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 12px;
          width: 22px;
          .label_10 {
            width: 22px;
            height: 23px;
          }
        }
        .text-group_14 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 10px;
        }
      }
      .image-text_15 {
        width: 60px;
        height: 45px;
        margin-left: 16px;
        .image-wrapper_8 {
          height: 23px;
          background: url(/static/lanhu_gerenzhongxin2/b2d7338e8fb843e1a1ab90ef7c472ae1_mergeImage.png)
            100% no-repeat;
          background-size: 100% 100%;
          margin-left: 19px;
          width: 22px;
          .label_11 {
            width: 22px;
            height: 23px;
          }
        }
        .text-group_15 {
          width: 60px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 10px;
        }
      }
      .image-text_16 {
        width: 48px;
        height: 45px;
        margin-left: 16px;
        .image_7 {
          width: 18px;
          height: 22px;
          margin-left: 15px;
        }
        .text-group_16 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 11px;
        }
      }
    }
  }
  .group_10 {
    width: 375px;
    height: 312px;
    .group_11 {
      width: 350px;
      height: 20px;
      margin: 16px 0 0 12px;
      .text_5 {
        width: 64px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 2px;
      }
      .text_6 {
        width: 64px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 100px;
        margin: 2px 0 0 28px;
      }
      .text_7 {
        width: 64px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 100px;
        margin: 2px 0 0 27px;
      }
      .image-text_17 {
        width: 51px;
        height: 20px;
        margin-left: 52px;
        .text-group_17 {
          width: 26px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin-top: 4px;
        }
        .thumbnail_5 {
          width: 20px;
          height: 20px;
        }
      }
    }
    .group_12 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 50px;
      width: 51px;
      height: 6px;
      margin: 3px 0 0 17px;
    }
    .list_1 {
      width: 351px;
      height: 242px;
      justify-content: space-between;
      margin: 10px 0 15px 12px;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 351px;
        height: 116px;
        margin-bottom: 10px;
        .image-text_18 {
          position: relative;
          width: 241px;
          height: 91px;
          margin: 12px 0 0 12px;
          .image_8 {
            width: 91px;
            height: 91px;
          }
          .text-group_18 {
            width: 138px;
            height: 87px;
            margin: 2px 0 0 11px;
            .text_8 {
              width: 90px;
              height: 15px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 15px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
            }
            .text_9 {
              width: 124px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(152, 152, 152, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin-top: 10px;
            }
            .box_7 {
              width: 138px;
              height: 18px;
              margin-top: 32px;
              .text-wrapper_2 {
                width: 41px;
                height: 18px;
                overflow-wrap: break-word;
                font-size: 0;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                line-height: 100px;
                .text_10 {
                  width: 41px;
                  height: 18px;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 8px;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  line-height: 100px;
                }
                .text_11 {
                  width: 41px;
                  height: 18px;
                  overflow-wrap: break-word;
                  color: rgba(231, 96, 81, 1);
                  font-size: 18px;
                  font-family: PingFang SC-Medium;
                  font-weight: 500;
                  text-align: left;
                  white-space: nowrap;
                  line-height: 100px;
                }
              }
              .text_12 {
                width: 52px;
                height: 12px;
                overflow-wrap: break-word;
                color: rgba(153, 153, 153, 1);
                font-size: 12px;
                font-family: PingFang SC-Regular;
                text-decoration: line-through;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 100px;
                margin-top: 3px;
              }
            }
          }
          .text-wrapper_3 {
            background-image: linear-gradient(
              90deg,
              rgba(255, 239, 190, 1) 0,
              rgba(254, 230, 157, 1) 100%
            );
            border-radius: 10px 10px 0px 10px;
            height: 15px;
            width: 36px;
            margin: 73px 0 0 -93px;
            .text_13 {
              width: 24px;
              height: 8px;
              overflow-wrap: break-word;
              color: rgba(98, 59, 4, 1);
              font-size: 8px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 4px 0 0 6px;
            }
          }
          .block_2 {
            border-radius: 4px;
            width: 77px;
            height: 15px;
            border: 1px solid rgba(231, 96, 81, 1);
            margin: 46px 0 0 -19px;
            .group_13 {
              background-color: rgba(231, 96, 81, 1);
              border-radius: 4px 0px 4px 0px;
              height: 15px;
              width: 17px;
              .group_14 {
                background-color: rgba(255, 207, 202, 1);
                width: 8px;
                height: 10px;
                margin: 2px 0 0 5px;
              }
            }
            .text_14 {
              width: 53px;
              height: 8px;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 10px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 100px;
              margin: 3px 3px 0 4px;
            }
          }
          .block_3 {
            background-image: linear-gradient(
              180deg,
              rgba(59, 41, 0, 1) 0,
              rgba(86, 54, 0, 1) 100%
            );
            border-radius: 4px;
            position: absolute;
            left: 102px;
            top: 46px;
            width: 56px;
            height: 15px;
            .image-text_19 {
              width: 45px;
              height: 10px;
              margin: 3px 0 0 6px;
              .block_4 {
                background-color: rgba(255, 234, 184, 1);
                width: 10px;
                height: 10px;
              }
              .text-group_19 {
                width: 32px;
                height: 10px;
                overflow-wrap: break-word;
                color: rgba(255, 234, 184, 1);
                font-size: 10px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 100px;
              }
            }
          }
        }
        .text-wrapper_4 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 60px;
          height: 25px;
          width: 66px;
          margin: 80px 12px 0 0;
          .text_15 {
            width: 48px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin: 7px 0 0 9px;
          }
        }
      }
    }
  }
  .group_15 {
    background-color: rgba(255, 255, 255, 1);
    width: 375px;
    height: 49px;
    margin: -1px 0 27px 0;
    .image-text_20 {
      width: 24px;
      height: 39px;
      margin-top: 6px;
      .label_12 {
        width: 22px;
        height: 22px;
        margin-left: 1px;
      }
      .text-group_20 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 5px;
      }
    }
    .image-text_21 {
      width: 24px;
      height: 38px;
      margin-top: 7px;
      .label_13 {
        width: 22px;
        height: 22px;
        margin-left: 1px;
      }
      .text-group_21 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 4px;
      }
    }
    .image-text_22 {
      width: 24px;
      height: 38px;
      margin-top: 7px;
      .label_14 {
        width: 22px;
        height: 22px;
        margin-left: 1px;
      }
      .text-group_22 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 4px;
      }
    }
    .image-text_23 {
      width: 24px;
      height: 38px;
      margin-top: 7px;
      .label_15 {
        width: 22px;
        height: 22px;
        margin-left: 1px;
      }
      .text-group_23 {
        width: 24px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 4px;
      }
    }
  }
  .group_16 {
    position: absolute;
    left: 0;
    top: 286px;
    width: 375px;
    height: 272px;
    .box_8 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      width: 351px;
      height: 106px;
      margin: 82px 0 0 12px;
      .block_5 {
        width: 326px;
        height: 14px;
        margin: 16px 0 0 13px;
        .text_16 {
          width: 56px;
          height: 14px;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 14px;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_17 {
          width: 48px;
          height: 12px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 12px;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 100px;
          margin: 1px 0 0 207px;
        }
        .thumbnail_6 {
          width: 5px;
          height: 8px;
          margin: 3px 0 0 10px;
        }
      }
      .block_6 {
        width: 326px;
        height: 45px;
        margin: 17px 0 14px 17px;
        .image-text_24 {
          width: 36px;
          height: 45px;
          .box_9 {
            height: 24px;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG3a152a939d0da18d03352ba14032f56e.png)
              100% no-repeat;
            background-size: 100% 100%;
            margin-left: 6px;
            width: 24px;
            .text-wrapper_5 {
              background-color: rgba(238, 12, 12, 1);
              border-radius: 50%;
              height: 12px;
              width: 12px;
              margin: -2px 0 0 18px;
              .text_18 {
                width: 5px;
                height: 10px;
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 10px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 100px;
                margin: 1px 0 0 3px;
              }
            }
          }
          .text-group_24 {
            width: 36px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 12px;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 9px;
          }
        }
        .image-text_25 {
          width: 36px;
          height: 45px;
          margin-left: 34px;
          .label_16 {
            width: 24px;
            height: 25px;
            margin-left: 6px;
          }
          .text-group_25 {
            width: 36px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 12px;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 8px;
          }
        }
        .image-text_26 {
          width: 36px;
          height: 45px;
          margin-left: 34px;
          .label_17 {
            width: 24px;
            height: 24px;
            margin-left: 6px;
          }
          .text-group_26 {
            width: 36px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 12px;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 9px;
          }
        }
        .image-text_27 {
          width: 36px;
          height: 45px;
          margin-left: 34px;
          .label_18 {
            width: 24px;
            height: 24px;
            margin-left: 6px;
          }
          .text-group_27 {
            width: 36px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 12px;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 9px;
          }
        }
        .image-text_28 {
          width: 52px;
          height: 45px;
          margin-left: 28px;
          .label_19 {
            width: 24px;
            height: 25px;
            margin-left: 12px;
          }
          .text-group_28 {
            width: 52px;
            height: 12px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 12px;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 8px;
          }
        }
      }
    }
    .box_10 {
      width: 350px;
      height: 63px;
      background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGfa2ef94064ba2c6d00469d8c83584628.png)
        100% no-repeat;
      background-size: 100% 100%;
      margin: 10px 0 11px 12px;
      .image-text_29 {
        width: 183px;
        height: 39px;
        margin: 13px 0 0 17px;
        .group_17 {
          height: 39px;
          background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG75681517c0627fcdab92e934834e4096.png)
            100% no-repeat;
          background-size: 100% 100%;
          width: 44px;
          .group_18 {
            height: 33px;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGe61461c9c7dc103306127b0493ef6139.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 39px;
            margin: 6px 0 0 -1px;
            .box_11 {
              width: 14px;
              height: 14px;
              background: url(/static/lanhu_gerenzhongxin2/6ae66c597d174fe6aa4c9f81b4de731f_mergeImage.png)
                100% no-repeat;
              background-size: 100% 100%;
              margin: 6px 0 0 13px;
            }
          }
        }
        .text-group_29 {
          width: 127px;
          height: 36px;
          .text_19 {
            width: 126px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 18px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
          }
          .text_20 {
            width: 127px;
            height: 10px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 10px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 100px;
            margin-top: 8px;
          }
        }
      }
      .text-wrapper_6 {
        background-color: rgba(0, 143, 116, 1);
        border-radius: 38px;
        height: 27px;
        width: 62px;
        margin: 18px 12px 0 76px;
        .text_21 {
          width: 49px;
          height: 11px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 11px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 8px 0 0 7px;
        }
      }
    }
    .box_12 {
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
      background-color: rgba(255, 255, 255, 1);
      border-radius: 10px;
      position: absolute;
      left: 12px;
      top: -28px;
      width: 351px;
      height: 100px;
      .box_13 {
        background-color: rgba(254, 247, 239, 1);
        border-radius: 10px;
        width: 153px;
        height: 71px;
        margin: 14px 0 0 13px;
        .image-text_30 {
          position: relative;
          width: 143px;
          height: 71px;
          margin-left: 10px;
          .text-group_30 {
            width: 88px;
            height: 46px;
            margin-top: 10px;
            .text_22 {
              width: 48px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 12px;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin-left: 18px;
            }
            .text_23 {
              width: 88px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 24px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin-top: 10px;
            }
          }
          .text-wrapper_7 {
            height: 71px;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNG876225c7c7c7e608ee2a030b4a8f33b3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 52px;
            .text_24 {
              width: 24px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 30px 0 0 20px;
            }
          }
          .section_1 {
            background-color: rgba(231, 96, 81, 1);
            position: absolute;
            left: 0;
            top: 10px;
            width: 11px;
            height: 12px;
          }
        }
      }
      .box_14 {
        background-color: rgba(237, 249, 243, 1);
        border-radius: 10px;
        width: 153px;
        height: 71px;
        margin: 14px 18px 0 14px;
        .image-text_31 {
          width: 143px;
          height: 71px;
          margin-left: 10px;
          .text-group_31 {
            width: 78px;
            height: 46px;
            margin-top: 10px;
            .text_25 {
              width: 60px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 12px;
              font-family: Inter-Medium;
              font-weight: 500;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin-left: 18px;
            }
            .text-wrapper_8 {
              width: 30px;
              height: 24px;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin-top: 10px;
              .text_26 {
                width: 30px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 24px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 100px;
              }
              .text_27 {
                width: 30px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 15px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: center;
                white-space: nowrap;
                line-height: 100px;
              }
            }
          }
          .text-wrapper_9 {
            height: 71px;
            background: url(/static/lanhu_gerenzhongxin2/FigmaDDSSlicePNGeafee3672b6f7f4ed5a9cff4ab408f2b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 52px;
            .text_28 {
              width: 24px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 100px;
              margin: 29px 0 0 18px;
            }
          }
        }
        .thumbnail_7 {
          width: 14px;
          height: 14px;
          margin: 9px 128px 0 -142px;
        }
      }
    }
  }
}
